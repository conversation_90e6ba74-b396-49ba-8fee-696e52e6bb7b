<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>划词工具栏测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }

        .test-text {
            background: #fff;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            margin: 15px 0;
            user-select: text;
            cursor: text;
        }

        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .instructions {
            background: #dbeafe;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #3b82f6;
            margin: 20px 0;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .icon-demo {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background: #4096FF;
            color: white;
            font-size: 10px;
            font-weight: bold;
            border-radius: 4px;
            margin-right: 8px;
        }

        .button-demo {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin-right: 8px;
            font-size: 14px;
        }

        .code {
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI助手划词工具栏测试</h1>
        
        <div class="instructions">
            <strong>📋 测试说明：</strong>
            <p>请在下面的文本区域中选中任意文字，观察划词工具栏的显示效果和功能。</p>
        </div>

        <div class="test-section">
            <h2>🎯 主要功能测试</h2>
            <div class="test-text">
                这是一段用于测试AI助手划词工具栏功能的示例文本。请选中这段文字的任意部分，观察工具栏的显示效果。工具栏应该包含以下按钮：
                <span class="highlight">AI助手面板入口、总结、翻译、更多功能菜单</span>。
            </div>
            
            <h3>🔧 预期的工具栏布局：</h3>
            <div style="display: flex; align-items: center; padding: 10px; background: white; border-radius: 20px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); width: fit-content; margin: 10px 0;">
                <div class="icon-demo">AI</div>
                <div class="button-demo">📝 总结</div>
                <div class="button-demo">🌐 翻译</div>
                <span style="margin: 0 8px;">⋮</span>
                <span style="margin: 0 8px;">×</span>
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 AI助手图标按钮测试</h2>
            <div class="test-text">
                <strong>重点测试内容：</strong>AI助手图标按钮现在应该：
                <br>• 显示 <span class="code">chatLogo</span> 图标或默认的 <span class="icon-demo">AI</span> 图标
                <br>• 点击后调用 <span class="code">open-panel</span> 操作
                <br>• 功能为"打开AI助手面板"而不是"总结"
                <br>• 鼠标悬停时显示"AI助手面板"提示
            </div>
        </div>

        <div class="test-section">
            <h2>🎨 动画效果测试</h2>
            <div class="test-text">
                选中这段文字来测试新的动画效果。工具栏应该具有以下动画特性：
                <br>• <strong>淡入动画：</strong>300毫秒的平滑显示效果
                <br>• <strong>淡出动画：</strong>200毫秒的平滑隐藏效果  
                <br>• <strong>缩放效果：</strong>显示时从95%缩放到100%
                <br>• <strong>滑动效果：</strong>向上滑动进入效果
                <br>• <strong>缓动曲线：</strong>使用cubic-bezier(0.4, 0, 0.2, 1)
            </div>
        </div>

        <div class="test-section">
            <h2>📱 交互测试</h2>
            <div class="test-text">
                测试各种交互场景：
                <br>1. <strong>选中文字：</strong>工具栏应该出现在选中文字下方
                <br>2. <strong>点击AI图标：</strong>应该发送open-panel消息并关闭工具栏
                <br>3. <strong>点击总结按钮：</strong>应该显示AI处理弹窗
                <br>4. <strong>点击翻译按钮：</strong>应该显示AI处理弹窗
                <br>5. <strong>点击更多按钮：</strong>应该显示下拉菜单
                <br>6. <strong>点击关闭按钮：</strong>应该隐藏工具栏
                <br>7. <strong>点击页面其他地方：</strong>应该隐藏工具栏
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 技术实现验证</h2>
            <div class="feature-list">
                <li><strong>图标配置：</strong>AI助手按钮使用chatLogo图标，总结按钮不再有图标</li>
                <li><strong>动作映射：</strong>AI助手按钮action为'open-panel'</li>
                <li><strong>消息传递：</strong>点击AI助手按钮发送chrome.runtime.sendMessage</li>
                <li><strong>动画系统：</strong>使用CSS transition和transform实现流畅动画</li>
                <li><strong>状态管理：</strong>正确处理显示/隐藏状态切换</li>
            </div>
        </div>

        <div class="test-section">
            <h2>📝 长文本测试</h2>
            <div class="test-text">
                这是一段较长的文本，用于测试工具栏在不同文本长度下的表现。人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。人工智能可以对人的意识、思维的信息过程的模拟。人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f0f9ff; border-radius: 8px; text-align: center;">
            <p><strong>🎉 测试完成后，请验证：</strong></p>
            <p>1. AI助手图标按钮是否正确显示和工作</p>
            <p>2. 动画效果是否流畅自然</p>
            <p>3. 所有交互功能是否正常</p>
            <p>4. 控制台是否有相关的日志输出</p>
        </div>
    </div>

    <script>
        // 添加一些调试信息
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 划词工具栏测试页面已加载');
            console.log('📋 请选中页面中的文字来测试工具栏功能');
        });

        // 监听选择事件
        document.addEventListener('selectionchange', function() {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                console.log('✅ 检测到文字选择:', selection.toString().trim());
            }
        });
    </script>
</body>
</html>
